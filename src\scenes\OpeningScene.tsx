import React from 'react';
import { AbsoluteFill, useCurrentFrame, useVideoConfig, interpolate } from 'remotion';
import AnimatedText from '../components/AnimatedText';
import { SceneProps } from '../types';
// React-bits imports for enhanced animations
import { Aurora } from 'react-bits/backgrounds';
import { SplitText } from 'react-bits/text-animations';
import { ParticleField } from 'react-bits/backgrounds';

export const OpeningScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {
  const currentFrame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 背景渐变动画
  const backgroundOpacity = interpolate(
    currentFrame,
    [0, 30],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 主标题动画时机
  const titleStartFrame = 30;
  const subtitleStartFrame = 90;
  const questionStartFrame = 150;

  return (
    <AbsoluteFill className="bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* 背景装饰 */}
      <div 
        className="absolute inset-0 bg-black/20"
        style={{ opacity: backgroundOpacity }}
      />
      
      {/* 动态背景粒子效果 */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => {
          const delay = i * 5;
          const opacity = interpolate(
            currentFrame,
            [delay, delay + 60],
            [0, 0.3],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
            }
          );
          
          return (
            <div
              key={i}
              className="absolute w-2 h-2 bg-white rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                opacity,
                animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,
              }}
            />
          );
        })}
      </div>

      {/* 主要内容 */}
      <div className="flex flex-col items-center justify-center h-full text-center px-8">
        {/* 主标题 */}
        <AnimatedText
          text="早睡是伪命题吗？"
          startFrame={titleStartFrame}
          className="text-6xl font-bold text-white mb-8"
          animationType="reveal"
          duration={60}
        />

        {/* 副标题 */}
        <AnimatedText
          text="很多人相信：只要早睡，就能变健康、变聪明，甚至改变人生。"
          startFrame={subtitleStartFrame}
          className="text-2xl text-gray-200 mb-6 max-w-4xl leading-relaxed"
          animationType="fade"
          duration={80}
        />

        {/* 引导问题 */}
        <AnimatedText
          text="但问题是，早睡真的是万能的吗？还是一个伪命题？"
          startFrame={questionStartFrame}
          className="text-3xl font-semibold text-yellow-300 max-w-3xl leading-relaxed"
          animationType="typewriter"
          duration={100}
        />

        {/* 装饰性图标 */}
        <div 
          className="mt-12 text-6xl"
          style={{
            opacity: interpolate(
              currentFrame,
              [questionStartFrame + 100, questionStartFrame + 130],
              [0, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }
            ),
            transform: `scale(${interpolate(
              currentFrame,
              [questionStartFrame + 100, questionStartFrame + 130],
              [0.5, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }
            )})`,
          }}
        >
          🤔
        </div>
      </div>

      {/* 底部提示 */}
      <div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        style={{
          opacity: interpolate(
            currentFrame,
            [durationInFrames - 60, durationInFrames - 30],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
            }
          ),
        }}
      >
        <div className="text-white/70 text-sm">
          让我们一起探索真相
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default OpeningScene;
